import { Image, PrimaryButton } from "microapps";
import { useState, useEffect } from "react";
import './MainView.scss';

interface GameMode {
  id: string;
  enabled: boolean;
  image: string;
  mode: "ia_vs_player";
  buttonText: string;
  description: string;
}

interface MainViewProps {
  handleStartGame: (mode: "ia_vs_player") => Promise<void>;
  handleShowRules: () => void;
  isStartingGame: boolean;
  isReady: boolean;
}

const MainView: React.FC<MainViewProps> = ({
  handleStartGame,
  handleShowRules,
  isStartingGame,
  isReady,
}) => {
  const [gameModes, setGameModes] = useState<GameMode[]>([]);

  const [fadeClass, setFadeClass] = useState<{
    fade1: boolean;
    fade2: boolean;
    fade3: boolean;
    fade4: boolean;
  }>({ fade1: false, fade2: false, fade3: false, fade4: false });

  useEffect(() => {
    const loadGameModes = async () => {
      try {
        const response = await fetch('/game-modes.json');
        if (!response.ok)
          throw new Error(`HTTP error! status: ${response.status}`);
        const data = await response.json();
        setGameModes(data.gameModes || []);
      } catch (error) {
        console.error('Error loading game modes:', error);
      }
    };

    loadGameModes();
  }, []);

  useEffect(() => {
    const timeouts = [
      setTimeout(() => setFadeClass(prev => ({ ...prev, fade1: true })), 0),
      setTimeout(() => setFadeClass(prev => ({ ...prev, fade2: true })), 400),
      setTimeout(() => setFadeClass(prev => ({ ...prev, fade3: true })), 800),
      setTimeout(() => setFadeClass(prev => ({ ...prev, fade4: true })), 1200),
    ];

    return () => timeouts.forEach(clearTimeout);
  }, []);

  return (
    <div className="mainview">

      <div className="menu-left">
        <button onClick={handleShowRules} className={`rules-button ${fadeClass.fade4 ? 'fade' : ''}`}>
          <Image
            width="100%"
            aspectRatio="1:1"
            src="assets/game/book.png"
            alt="Book"
            className="book-image"
          />
          <p className="rules-text bold body1">Reglas</p>
        </button>
      </div>

      <div className="center">
        <div className={`center-header ${fadeClass.fade1 ? 'fade' : ''}`}>
          <h1 className="header-title">Enygma</h1>
          <p className="header-subtitle body1">
            ¿Puedes adivinar el personaje que está pensando Enygma?
          </p>
        </div>

        <div className="center-modes">
          {gameModes
            .filter(mode => mode.enabled)
            .map((mode) => (
              <div key={mode.id} className="modes-card">
                <Image
                  src={mode.image}
                  alt="Enygma"
                  className={`image-enygma ${fadeClass.fade2 ? 'fade' : ''}`}
                  width="250px"
                  aspectRatio="7:10"
                />

                <PrimaryButton
                  onClick={() => handleStartGame(mode.mode)}
                  isDisabled={isStartingGame || !isReady}
                  isLoading={isStartingGame || !isReady}
                  text={mode.buttonText}
                  className={`primary-button start-button ${fadeClass.fade3 ? 'fade' : ''}`}
                />
              </div>
            ))}
        </div>
      </div>
    </div>
  );
};

export default MainView;
