import {
  createContext,
  useContext,
  useState,
  useCallback,
  useEffect,
  type ReactNode,
} from "react";
import { aiContentSpeechWrapper } from "../services/AIContentSpeechWrapper";
import { useEventBus } from "./EventBusContext";

export type GameMode = "ia_vs_player";

/**
 * Fases del juego que determinan qué acciones están disponibles.
 * - setup: Preparación inicial (selección de modo, personaje)
 * - questioning: Fase de preguntas (usuario hace preguntas o responde)
 * - guessing: Fase de suposiciones (usuario intenta adivinar el personaje)
 * - finished: Juego terminado (se muestra resultado)
 */
export type GamePhase = "setup" | "questioning" | "guessing" | "finished";

/**
 * Estructura principal de una sesión de juego
 * Contiene todo el estado necesario para una partida completa
 */
export interface GameSession {
  /** Identificador único de la sesión */
  id: string;
  /** Modo de juego seleccionado */
  mode: GameMode;
  /** Fase actual del juego */
  phase: GamePhase;
  /** Momento de inicio del juego */
  startTime: Date;
  /** Momento de finalización (si ha terminado) */
  endTime?: Date;

  // === ESTADO DEL JUEGO ===
  /** Número de preguntas realizadas */
  questionCount: number;
  /** Máximo de preguntas permitidas */
  maxQuestions: number;
  /** Personaje actual (si está definido) */
  currentCharacter?: string;
  /** Nivel de confianza de la IA (0-100) */
  aiConfidence: number;
  /** Rol del jugador en esta sesión */
  playerRole: PlayerRole;
  /** Historial completo de mensajes */
  messages: GameMessage[];

  // === RESULTADO DEL JUEGO ===
  /** Ganador de la partida */
  winner?: "ai" | "user" | "draw";
  /** Última suposición realizada */
  finalGuess?: string;
  /** Si la suposición final fue correcta */
  wasCorrect?: boolean;
}

/**
 * Estructura de un mensaje en el juego
 * Incluye metadatos para análisis y presentación
 */
export interface GameMessage {
  /** ID único del mensaje */
  id: string;
  /** Contenido del mensaje */
  text: string;
  /** Quién envió el mensaje */
  sender: "user" | "ai";
  /** Momento del envío */
  timestamp: Date;
  /** Tipo de mensaje para clasificación */
  type: "question" | "answer" | "guess" | "hint" | "system" | "presentation";
  /** Nivel de confianza (solo para mensajes de IA) */
  confidence?: number;
  /** Respuesta validada (solo para respuestas del usuario) */
  validatedResponse?: "yes" | "no" | "maybe" | "unknown";
  /** Indica si este mensaje cuenta para el límite de preguntas (default: true para questions) */
  countsAsQuestion?: boolean;
}

/**
 * Rol del jugador en la sesión actual
 * - guesser: El jugador hace preguntas (modo "ia_vs_player")
 */
export type PlayerRole = "guesser";

export interface EnygmaGameContextProps {
  // Estado actual
  session: GameSession | null;
  currentPhase: GamePhase;
  playerRole: PlayerRole;

  // Acciones del juego
  startNewGame: (mode: GameMode, character?: string) => Promise<void>;
  askQuestion: (question: string, countsAsQuestion?: boolean) => Promise<void>;
  askInitialMessage: (message: string) => Promise<void>;
  makeGuess: (character: string) => Promise<boolean>;
  endGame: (reason: "victory" | "defeat" | "timeout" | "quit") => void;

  // Propiedades computadas
  canAskQuestion: boolean;
  canMakeGuess: boolean;
  questionsRemaining: number;
  gameProgress: number;
}

/**
 * ========================================================================
 * EnygmaGameProvider - Contexto del Juego Enygma
 *
 * Gestiona:
 * - Lógica pura del juego (sin coordinación de servicios)
 * - Estado de sesiones y progreso
 * - Procesamiento de mensajes y respuestas
 * - Análisis e insights del juego
 * - Validaciones de entrada según reglas
 * =======================================================================
 */

const EnygmaGameContext = createContext<EnygmaGameContextProps | undefined>(
  undefined
);

/**
 * Hook personalizado para acceder al contexto del juego
 * Incluye validación de uso dentro del EnygmaGameProvider
 *
 * @returns {EnygmaGameContextProps} Propiedades y métodos del contexto
 * @throws {Error} Si se usa fuera del EnygmaGameProvider
 */
export const useEnygmaGame = () => {
  const context = useContext(EnygmaGameContext);
  if (!context) {
    throw new Error("useEnygmaGame must be used within EnygmaGameProvider");
  }
  return context;
};

/**
 * Provider principal del juego Enygma
 * Debe envolver toda la lógica de juego y ser el contexto más interno
 *
 * @param {Object} props - Props del componente
 * @param {ReactNode} props.children - Componentes hijos
 */
export const EnygmaGameProvider = ({ children }: { children: ReactNode }) => {
  const { emit } = useEventBus();
  const [session, setSession] = useState<GameSession | null>(null);

  // Emitir evento cuando el juego termina
  useEffect(() => {
    if (session && session.phase === "finished" && session.endTime) {
      console.log('🎮 [EnygmaGame] Emitiendo evento game:end');
      const gameResult = {
        winner: session.winner,
        finalGuess: session.finalGuess,
        wasCorrect: session.wasCorrect,
        currentCharacter: session.currentCharacter,
        questionsUsed: session.questionCount,
        maxQuestions: session.maxQuestions,
        mode: session.mode,
        endTime: session.endTime,
      };

      emit("game:end", {
        session,
        result: gameResult,
      });
    }
  }, [session, emit]);

  /**
   * Fase actual del juego
   * Determina qué acciones están disponibles
   */
  const currentPhase: GamePhase = session?.phase || "setup";

  /**
   * Rol del jugador basado en el modo de juego
   * - "ia_vs_player": El usuario hace preguntas (guesser)
   */
  const playerRole: PlayerRole = session?.playerRole || "guesser";

  /**
   * Determina si el usuario puede hacer una pregunta
   * Requiere estar en fase de questioning y no haber agotado las preguntas
   */
  const canAskQuestion =
    session?.phase === "questioning" &&
    session.questionCount < session.maxQuestions &&
    session.playerRole === "guesser";

  /**
   * Determina si el usuario puede hacer una suposición
   * Disponible en fases de questioning y guessing
   */
  const canMakeGuess =
    session?.phase === "questioning" || session?.phase === "guessing";

  /**
   * Calcula preguntas restantes en la sesión
   */
  const questionsRemaining = session
    ? session.maxQuestions - session.questionCount
    : 0;

  /**
   * Calcula el progreso del juego en porcentaje
   */
  const gameProgress = session
    ? (session.questionCount / session.maxQuestions) * 100
    : 0;

  // === FUNCIONES PRINCIPALES ===

  /**
   * Finaliza el juego automáticamente cuando se alcanzan las 20 preguntas - NUEVO
   */
  const checkGameEnd = useCallback(() => {
    if (!session) return;

    // Verificar si se alcanzó el límite de preguntas
    if (
      session.questionCount >= session.maxQuestions &&
      session.phase === "questioning"
    ) {
      // Determinar ganador basado en quien no pudo completar su objetivo
      const winner: "ai" | "user" | "draw" | undefined = session.playerRole === "guesser" ? "ai" : "user";

      setSession((prev) => {
        if (!prev) return null;

        const finalSession: GameSession = {
          ...prev,
          phase: "finished" as GamePhase,
          endTime: new Date(),
          winner,
          wasCorrect: false, // No se acertó dentro del límite
        };

        // Guardar resultado en localStorage
        const gameResult = {
          winner,
          finalGuess: undefined,
          wasCorrect: false,
          currentCharacter: prev.currentCharacter,
          questionsUsed: prev.questionCount,
          maxQuestions: prev.maxQuestions,
          mode: prev.mode,
          endTime: new Date(),
        };

        localStorage.setItem(
          "enygma_last_game_result",
          JSON.stringify(gameResult)
        );

        return finalSession;
      });
    }
  }, [session, emit]);

  /**
   * Procesa una suposición sobre el personaje
   *
   * @param character - Nombre del personaje supuesto
   * @returns Si la suposición fue correcta
   */
  const makeGuess = useCallback(
    async (character: string): Promise<boolean> => {
      if (!session) return false;

      // Crear mensaje de suposición
      const guessMessage: GameMessage = {
        id: `msg-${Date.now()}`,
        text: `¿Es ${character}?`,
        sender: session.playerRole === "guesser" ? "user" : "ai",
        timestamp: new Date(),
        type: "guess",
      };

      try {
        // Obtener respuesta de la IA para validar la suposición
        const response =
          await aiContentSpeechWrapper.generateResponseWithSpeech(
            `¿Es ${character}?`,
            session.mode,
            session.currentCharacter
          );

        let isCorrect = false;

        if (response.ok) {
          // Usar datos estructurados si están disponibles
          if (response.gameData) {
            isCorrect = response.gameData.acertado;
            // En makeGuess, el juego siempre termina independientemente de juego_finalizado
          } else {
            // Fallback: parsear la respuesta de texto para determinar si es correcta
            const responseText = response.output.toLowerCase();

            // Buscar indicadores de acierto en la respuesta
            isCorrect =
              responseText.includes("correcto") ||
              responseText.includes("exacto") ||
              responseText.includes("acertado") ||
              responseText.includes("¡sí!") ||
              responseText.includes("¡correcto!") ||
              responseText.includes("has acertado") ||
              responseText.includes("es correcto");

            // También verificar si la IA dice explícitamente que no es correcto
            if (
              responseText.includes("no es") ||
              responseText.includes("incorrecto") ||
              responseText.includes("no has acertado")
            ) {
              isCorrect = false;
            }
          }
        }

        // Determinar ganador basado en el resultado
        let winner: "ai" | "user" | "draw";

        if (isCorrect) {
          // Si la suposición es correcta, gana quien hizo la suposición
          winner = session.playerRole === "guesser" ? "user" : "ai";
        } else {
          // Si la suposición es incorrecta, gana el oponente
          winner = session.playerRole === "guesser" ? "ai" : "user";
        }

        // Actualizar sesión con el resultado final
        setSession((prev) => {
          if (!prev) return null;

          const finalSession: GameSession = {
            ...prev,
            phase: "finished" as GamePhase,
            endTime: new Date(),
            messages: [...prev.messages, guessMessage],
            winner,
            finalGuess: character,
            wasCorrect: isCorrect,
          };

          // Guardar resultado en localStorage para persistencia
          const gameResult = {
            winner,
            finalGuess: character,
            wasCorrect: isCorrect,
            currentCharacter: prev.currentCharacter,
            questionsUsed: prev.questionCount,
            maxQuestions: prev.maxQuestions,
            mode: prev.mode,
            endTime: new Date(),
          };

          localStorage.setItem(
            "enygma_last_game_result",
            JSON.stringify(gameResult)
          );

          return finalSession;
        });

        return isCorrect;
      } catch (error) {
        console.error(`❌ [game] 🎯 Error al procesar suposición`, error);
        return false;
      }
    },
    [session, emit]
  );

  /**
   * Inicia una nueva partida del juego
   *
   * @param mode - Modo de juego seleccionado
   * @param character - Personaje opcional
   */
  const startNewGame = useCallback(
    async (mode: GameMode = "ia_vs_player", character?: string): Promise<void> => {
      try {
        let gameCharacter = character;

        if (mode === "ia_vs_player") {
          const generatedCharacter =
            await aiContentSpeechWrapper.generateCharacterWithAnnouncement();

          if (generatedCharacter) {
            gameCharacter = generatedCharacter;
          } else {
            console.warn(
              "⚠️ No se pudo generar personaje, usando personaje por defecto"
            );
            gameCharacter = "un personaje misterioso";
          }
        }

        // Crear nueva sesión con configuración inicial
        const newSession: GameSession = {
          id: `game-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
          mode: "ia_vs_player",
          phase: "questioning",
          startTime: new Date(),
          questionCount: 0,
          maxQuestions: 20,
          currentCharacter: gameCharacter,
          aiConfidence: 0,
          messages: [],
          playerRole: "guesser",
        };

        // Actualizar estado
        setSession(newSession);
      } catch (error) {
        console.error("❌ Error al iniciar partida", error);
        throw error;
      }
    },
    []
  );

  /**
   * Procesa una pregunta del usuario (cuando es guesser)
   *
   * @param question - Pregunta formulada por el usuario
   * @param countsAsQuestion - Si debe contar para el límite de preguntas (default: true)
   */
  const askQuestion = useCallback(
    async (
      question: string,
      countsAsQuestion: boolean = true
    ): Promise<void> => {
      // ... código existente hasta antes de setSession ...

      try {
        // Obtener respuesta de la IA, pasando el personaje si está disponible
        if (!session) {
          throw new Error("No hay sesión activa para procesar la pregunta.");
        }

        // Crear mensaje de pregunta del usuario
        const userMessage: GameMessage = {
          id: `msg-${Date.now()}`,
          text: question,
          sender: "user",
          timestamp: new Date(),
          type: "question",
        };

        const response =
          await aiContentSpeechWrapper.generateResponseWithSpeech(
            question,
            session.mode,
            session.currentCharacter
          );

        if (response.ok) {
          // Verificar si el juego ha finalizado según la IA
          const gameFinished = response.gameData?.juego_finalizado || false;
          const isCorrectGuess = response.gameData?.acertado || false;

          console.log('🎮 [EnygmaGame] Respuesta de IA:', {
            output: response.output.substring(0, 100) + '...',
            gameData: response.gameData,
            gameFinished,
            isCorrectGuess
          });

          // Crear mensaje de respuesta de la IA
          const aiMessage: GameMessage = {
            id: `msg-${Date.now() + 1}`,
            text: response.output,
            sender: "ai",
            timestamp: new Date(),
            type: "answer",
          };

          // Actualizar sesión con ambos mensajes
          setSession((prev) => {
            if (!prev) return null;

            // Solo incrementar contador si la pregunta cuenta
            const newQuestionCount = countsAsQuestion
              ? prev.questionCount + 1
              : prev.questionCount;

            // Determinar la nueva fase del juego
            let newPhase: GamePhase;
            if (gameFinished) {
              newPhase = "finished";
            } else if (newQuestionCount >= prev.maxQuestions) {
              newPhase = "guessing";
            } else {
              newPhase = "questioning";
            }

            const updatedSession = {
              ...prev,
              questionCount: newQuestionCount,
              phase: newPhase as GamePhase,
              messages: [...prev.messages, userMessage, aiMessage],
            };

            // Si el juego ha finalizado según la IA, finalizar inmediatamente
            if (gameFinished) {
              console.log('🎮 [EnygmaGame] Finalizando juego por IA');
              const winner: "ai" | "user" | "draw" | undefined = isCorrectGuess ? "user" : "ai";

              const finalSession: GameSession = {
                ...updatedSession,
                phase: "finished" as GamePhase,
                endTime: new Date(),
                winner,
                wasCorrect: isCorrectGuess,
                finalGuess: isCorrectGuess ? prev.currentCharacter : undefined,
              };

              console.log('🎮 [EnygmaGame] Sesión final:', {
                phase: finalSession.phase,
                winner: finalSession.winner,
                wasCorrect: finalSession.wasCorrect
              });

              // Guardar resultado en localStorage
              const gameResult = {
                winner,
                finalGuess: isCorrectGuess ? prev.currentCharacter : undefined,
                wasCorrect: isCorrectGuess,
                currentCharacter: prev.currentCharacter,
                questionsUsed: newQuestionCount,
                maxQuestions: prev.maxQuestions,
                mode: prev.mode,
                endTime: new Date(),
              };

              localStorage.setItem(
                "enygma_last_game_result",
                JSON.stringify(gameResult)
              );

              return finalSession;
            }

            // Verificar si se debe finalizar el juego por límite de preguntas
            if (newQuestionCount >= prev.maxQuestions && !gameFinished) {
              setTimeout(() => checkGameEnd(), 100); // Pequeño delay para que se actualice el estado
            }

            return updatedSession;
          });
        } else {
          throw new Error("Error al obtener respuesta de la IA");
        }
      } catch (error) {
        console.error(`❌ [game] 💬 Error al procesar pregunta`, error);
        throw error;
      }
    },
    [session, canAskQuestion, checkGameEnd]
  );

  /**
   * Envía un mensaje de inicialización/presentación que NO cuenta como pregunta
   *
   * @param message - Mensaje de presentación (ej: "Hola")
   */
  const askInitialMessage = useCallback(
    async (message: string): Promise<void> => {
      // Usar askQuestion con countsAsQuestion = false
      await askQuestion(message, false);
    },
    [askQuestion]
  );



  /**
   * Finaliza el juego con una razón específica
   *
   * @param reason - Razón por la que termina el juego
   */
  const endGame = useCallback(
    (reason: "victory" | "defeat" | "timeout" | "quit") => {
      if (!session) return;

      // log.info("game", `Terminando juego: ${reason}`, {
      //   sessionId: session.id
      // });

      // Mapear razón a ganador
      const winner = {
        victory: "user",
        defeat: "ai",
        timeout: undefined,
        quit: undefined,
      }[reason] as "user" | "ai" | undefined;

      setSession((prev) => {
        if (!prev) return null;

        return {
          ...prev,
          phase: "finished" as GamePhase,
          endTime: new Date(),
          winner,
        };
      });
    },
    [session]
  );

  // === VALOR DEL CONTEXTO ===
  const contextValue: EnygmaGameContextProps = {
    // Estado
    session,
    currentPhase,
    playerRole,

    // Acciones
    startNewGame,
    askQuestion,
    askInitialMessage,
    makeGuess,
    endGame,

    // Propiedades computadas
    canAskQuestion,
    canMakeGuess,
    questionsRemaining,
    gameProgress,
  };

  return (
    <EnygmaGameContext.Provider value={contextValue}>
      {children}
    </EnygmaGameContext.Provider>
  );
};
